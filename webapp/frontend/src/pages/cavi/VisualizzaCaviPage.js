import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  IconButton,
  Chip,
  CircularProgress,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import RefreshIcon from '@mui/icons-material/Refresh';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useGlobalContext } from '../../context/GlobalContext';
import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';
import caviService from '../../services/caviService';
import CavoForm from '../../components/cavi/CavoForm';
import { normalizeInstallationStatus } from '../../utils/validationUtils';
import CaviFilterableTable from '../../components/cavi/CaviFilterableTable';
import './CaviPage.css';

const VisualizzaCaviPage = () => {
  const { isImpersonating, user } = useAuth();
  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();
  const navigate = useNavigate();
  const [cantiereId, setCantiereId] = useState(null);
  const [cantiereName, setCantiereName] = useState('');
  const [caviAttivi, setCaviAttivi] = useState([]);
  const [caviSpare, setCaviSpare] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // Stato per le notifiche
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  // Rimosso stato viewMode

  // Stato per il dialogo dei dettagli del cavo
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  // Stato per le statistiche
  const [stats, setStats] = useState({
    totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },
    metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },
    stati: []
  });
  const [loadingStats, setLoadingStats] = useState(false);

  // Stato per la gestione delle revisioni
  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);
  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');
  const [revisioneCorrente, setRevisioneCorrente] = useState('');

  // Rimosso stato per il debug

  // Funzione per caricare gli stati di installazione disponibili
  const loadStatiInstallazione = () => {
    // Usa i valori dell'enum StatoInstallazione
    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);
  };

  // Funzione per caricare le revisioni disponibili
  const loadRevisioni = async (cantiereIdToUse) => {
    try {
      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);

      // Carica la revisione corrente
      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);
      console.log('Revisione corrente:', revisioneCorrenteData);
      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);

      // Carica tutte le revisioni disponibili
      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);
      console.log('Revisioni disponibili:', revisioniData);
      setRevisioniDisponibili(revisioniData.revisioni || []);

      // LOGICA REVISIONI: La revisione corrente è quella di default
      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente
      console.log('Logica revisioni: usando revisione corrente di default');
    } catch (error) {
      console.error('Errore nel caricamento delle revisioni:', error);
    }
  };

  // Funzione per gestire il cambio di revisione
  const handleRevisioneChange = (event) => {
    const nuovaRevisione = event.target.value;

    // LOGICA REVISIONI:
    // - Se vuoto o "corrente" -> usa revisione corrente (non specificare parametro)
    // - Se specifica -> usa quella revisione per visualizzazione storica
    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {
      setRevisioneSelezionata('');
      console.log('Passaggio a revisione corrente (default)');
    } else {
      setRevisioneSelezionata(nuovaRevisione);
      console.log('Passaggio a revisione storica:', nuovaRevisione);
    }

    // Ricarica le statistiche con la nuova revisione
    if (cantiereId) {
      loadStatsForRevisione(cantiereId, nuovaRevisione === 'corrente' ? null : nuovaRevisione);
    }
  };

  // Funzione per caricare le statistiche per una revisione specifica
  const loadStatsForRevisione = async (cantiereIdToUse, revisione) => {
    try {
      setLoadingStats(true);
      console.log('Caricamento statistiche per revisione:', revisione || 'corrente (default)');
      const statsData = await caviService.getCaviStats(cantiereIdToUse, revisione);
      console.log('Statistiche caricate:', statsData);

      // Mostra quale revisione è stata effettivamente utilizzata
      if (statsData.revisione_utilizzata) {
        console.log('Revisione utilizzata dal backend:', statsData.revisione_utilizzata);
      }

      setStats(statsData);
    } catch (error) {
      console.error('Errore nel caricamento delle statistiche per revisione:', error);
    } finally {
      setLoadingStats(false);
    }
  };

  // Stato per filtri e ordinamento
  const [filters, setFilters] = useState({
    stato_installazione: '',
    tipologia: '',
    sort_by: '',
    sort_order: 'asc'
  });

  // Opzioni per i filtri
  const [statiInstallazione, setStatiInstallazione] = useState([]);
  const [tipologieCavi, setTipologieCavi] = useState([]);

  // Rimossa funzione di debug

  // Funzione per caricare i cavi
  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento
  const fetchCavi = async (silentLoading = false) => {
    try {
      if (!silentLoading) {
        setLoading(true);
      }
      console.log('Caricamento cavi per cantiere:', cantiereId);

      // Verifica che cantiereId sia valido
      if (!cantiereId) {
        console.error('fetchCavi: cantiereId non valido:', cantiereId);
        setError('ID cantiere non valido o mancante. Ricarica la pagina.');
        setLoading(false);
        return;
      }

      // Recupera il cantiereId dal localStorage come fallback
      let cantiereIdToUse = cantiereId;
      if (!cantiereIdToUse) {
        cantiereIdToUse = localStorage.getItem('selectedCantiereId');
        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);
        if (!cantiereIdToUse) {
          console.error('Impossibile trovare un ID cantiere valido');
          setError('ID cantiere non trovato. Ricarica la pagina.');
          setLoading(false);
          return;
        }
      }

      // Carica i cavi attivi
      console.log('Caricamento cavi attivi (tipo_cavo=0)...');
      let attivi = [];
      try {
        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);
        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);
      } catch (attiviError) {
        console.error('Errore nel caricamento dei cavi attivi:', attiviError);
        // Continua con un array vuoto
        attivi = [];
      }

      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi
      if (attivi && attivi.length > 0) {
        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);
        if (caviSpareTraAttivi.length > 0) {
          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);
        }
      }

      setCaviAttivi(attivi || []);

      // Carica i cavi SPARE con la nuova funzione dedicata
      let spare = [];
      try {
        console.log('Caricamento cavi SPARE con funzione dedicata...');
        spare = await caviService.getCaviSpare(cantiereIdToUse);
        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);
        if (spare && spare.length > 0) {
          console.log('Primo cavo SPARE:', spare[0]);
        }
      } catch (spareError) {
        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);
        // Se fallisce, prova con il metodo standard
        try {
          console.log('Tentativo con metodo standard...');
          spare = await caviService.getCavi(cantiereIdToUse, 3);
          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);
        } catch (standardError) {
          console.error('Errore anche con metodo standard:', standardError);
          // Continua con un array vuoto
          spare = [];
        }
      }
      setCaviSpare(spare || []);

      // Carica le statistiche
      try {
        console.log('Caricamento statistiche...');
        const statsData = await caviService.getCaviStats(cantiereIdToUse);
        console.log('Statistiche caricate:', statsData);

        // Verifica che statsData abbia la struttura attesa
        if (!statsData || typeof statsData !== 'object') {
          console.error('Statistiche non valide:', statsData);
          // Imposta un oggetto stats con struttura valida ma vuota
          setStats({
            totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },
            metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },
            stati: []
          });
        } else {
          // Assicurati che tutte le proprietà necessarie siano presenti
          const validStats = {
            totali: statsData.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },
            metrature: statsData.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },
            stati: statsData.stati || []
          };
          setStats(validStats);
        }
      } catch (statsError) {
        console.error('Errore nel caricamento delle statistiche:', statsError);
        // Continua con statistiche vuote ma con struttura valida
        setStats({
          totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },
          metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },
          stati: []
        });
      }

      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti
      setError('');
    } catch (error) {
      console.error('Errore generale nel caricamento dei cavi:', error);
      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);

      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste
      setTimeout(() => {
        // Verifica se siamo ancora in errore
        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {
          console.log('Errore persistente, tentativo di ricaricamento della pagina...');
          window.location.reload();
        }
      }, 5000); // 5 secondi di ritardo
    } finally {
      if (!silentLoading) {
        setLoading(false);
      }
    }
  };

  // Carica i dati del cantiere e dei cavi
  useEffect(() => {
    // Carica gli stati di installazione all'avvio
    loadStatiInstallazione();

    const fetchData = async () => {
      try {
        console.log('Inizializzazione VisualizzaCaviPage...');

        // Verifica che l'utente sia autenticato
        const token = localStorage.getItem('token');
        console.log('Token presente:', !!token);
        if (!token) {
          setError('Sessione scaduta. Effettua nuovamente il login.');
          setLoading(false);
          return;
        }

        // Recupera l'ID del cantiere selezionato dal localStorage
        let selectedCantiereId = localStorage.getItem('selectedCantiereId');
        let selectedCantiereName = localStorage.getItem('selectedCantiereName');

        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });
        console.log('Dati utente:', user);

        // Stampa tutti i dati nel localStorage per debug
        console.log('DEBUG - Tutti i dati nel localStorage:');
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          console.log(`${key}: ${localStorage.getItem(key)}`);
        }

        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT
        if (user?.role === 'cantieri_user') {
          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');

          // Verifica se l'utente ha un ID cantiere nei dati utente
          if (user.cantiere_id) {
            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);
            selectedCantiereId = user.cantiere_id.toString();
            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;

            // Salva l'ID e il nome del cantiere nel localStorage
            localStorage.setItem('selectedCantiereId', selectedCantiereId);
            localStorage.setItem('selectedCantiereName', selectedCantiereName);
            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);
          } else {
            // Tentativo di recupero dal token JWT
            try {
              console.log('Tentativo di decodifica del token JWT per recuperare l\'ID cantiere');
              const token = localStorage.getItem('token');
              if (token) {
                // Decodifica il token JWT (senza verifica della firma)
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));

                const payload = JSON.parse(jsonPayload);
                console.log('Payload del token JWT:', payload);

                if (payload.cantiere_id) {
                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);
                  selectedCantiereId = payload.cantiere_id.toString();
                  // Usa un nome generico se non disponibile
                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;

                  // Salva l'ID e il nome del cantiere nel localStorage
                  localStorage.setItem('selectedCantiereId', selectedCantiereId);
                  localStorage.setItem('selectedCantiereName', selectedCantiereName);
                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);
                }
              }
            } catch (e) {
              console.error('Errore durante la decodifica del token JWT:', e);
            }
          }
        }

        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug
        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {
          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');
          // Usa il primo cantiere disponibile (questo è solo per debug)
          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database
          selectedCantiereName = 'Cantiere Debug';

          // Salva l'ID e il nome del cantiere nel localStorage
          localStorage.setItem('selectedCantiereId', selectedCantiereId);
          localStorage.setItem('selectedCantiereName', selectedCantiereName);
          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);
        }

        // Verifica finale
        if (!selectedCantiereId) {
          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');
          setLoading(false);
          return;
        }

        // Verifica che l'ID del cantiere sia un numero valido
        const cantiereIdNum = parseInt(selectedCantiereId, 10);
        console.log('ID cantiere convertito a numero:', cantiereIdNum);
        if (isNaN(cantiereIdNum)) {
          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);
          setLoading(false);
          return;
        }

        // Usa il numero convertito, non la stringa
        setCantiereId(cantiereIdNum);
        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);

        // Carica le revisioni disponibili
        await loadRevisioni(cantiereIdNum);

        // Carica le statistiche dei cavi
        try {
          setLoadingStats(true);
          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);
          const statsData = await caviService.getCaviStats(cantiereIdNum);
          console.log('Statistiche cavi caricate:', statsData);
          setStats(statsData);

          // Estrai gli stati di installazione e le tipologie per i filtri
          if (statsData && statsData.stati) {
            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');
            setStatiInstallazione(stati);
          }

          if (statsData && statsData.tipologie) {
            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');
            setTipologieCavi(tipologie);
          }

          setLoadingStats(false);
        } catch (statsError) {
          console.error('Errore nel caricamento delle statistiche:', statsError);
          setLoadingStats(false);
          // Non interrompere il flusso se le statistiche falliscono
        }

        // Carica i cavi attivi con gestione degli errori migliorata
        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);
        try {
          // Imposta un timeout per evitare che la richiesta rimanga bloccata
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi
          });

          // Esegui la richiesta con un timeout di sicurezza e applica i filtri
          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);
          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);
          const attivi = await Promise.race([caviPromise, timeoutPromise]);

          console.log('Cavi attivi caricati:', attivi);
          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);
          if (attivi && attivi.length > 0) {
            console.log('Primo cavo attivo:', attivi[0]);
          } else {
            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);
          }
          setCaviAttivi(attivi || []);
        } catch (caviError) {
          console.error('Errore nel caricamento dei cavi attivi:', caviError);
          console.error('Dettagli errore cavi attivi:', {
            message: caviError.message,
            status: caviError.status,
            data: caviError.data,
            stack: caviError.stack,
            code: caviError.code,
            name: caviError.name,
            response: caviError.response ? {
              status: caviError.response.status,
              statusText: caviError.response.statusText,
              data: caviError.response.data
            } : 'No response'
          });

          // Non interrompere il flusso, continua con i cavi spare
          setCaviAttivi([]);
          console.warn('Continuazione del flusso dopo errore nei cavi attivi');

          // Aggiungi un messaggio di errore visibile all'utente
          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);
        }

        // Carica i cavi spare con gestione degli errori migliorata
        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);
        try {
          // Imposta un timeout per evitare che la richiesta rimanga bloccata
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi
          });

          // Esegui la richiesta con un timeout di sicurezza
          console.log('Iniziando chiamata API per cavi spare...');
          // Non applichiamo i filtri ai cavi spare, solo agli attivi
          const sparePromise = caviService.getCavi(cantiereIdNum, 3);
          const spare = await Promise.race([sparePromise, timeoutPromise]);

          console.log('Cavi spare caricati:', spare);
          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);
          if (spare && spare.length > 0) {
            console.log('Primo cavo spare:', spare[0]);
          } else {
            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);
          }
          setCaviSpare(spare || []);
        } catch (spareError) {
          console.error('Errore nel caricamento dei cavi spare:', spareError);
          console.error('Dettagli errore cavi spare:', {
            message: spareError.message,
            status: spareError.status,
            data: spareError.data,
            stack: spareError.stack,
            code: spareError.code,
            name: spareError.name,
            response: spareError.response ? {
              status: spareError.response.status,
              statusText: spareError.response.statusText,
              data: spareError.response.data
            } : 'No response'
          });

          // Non interrompere il flusso, imposta un array vuoto
          setCaviSpare([]);

          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi
          if (!error) {
            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);
          }
        }

        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base
        setLoading(false);

      } catch (err) {
        console.error('Errore nel caricamento dei cavi:', err);
        console.error('Dettagli errore generale:', {
          message: err.message,
          status: err.status || err.response?.status,
          data: err.data || err.response?.data,
          stack: err.stack
        });

        // Estrai il messaggio di errore dettagliato
        let errorMessage = 'Errore sconosciuto';

        if (err.message && err.message.includes('ID cantiere non valido')) {
          errorMessage = err.message;
        } else if (err.status === 401 || err.status === 403 ||
                  err.response?.status === 401 || err.response?.status === 403) {
          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';
        } else if (err.response?.data?.detail) {
          // Estrai il messaggio di errore dettagliato dall'API
          errorMessage = `Errore API: ${err.response.data.detail}`;
        } else if (err.code === 'ERR_NETWORK') {
          // Errore di rete
          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';
        } else if (err.message) {
          errorMessage = err.message;
        }

        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);

        // Imposta array vuoti per evitare errori di rendering
        setCaviAttivi([]);
        setCaviSpare([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]); // Ricarica i dati quando cambiano i filtri

  // I filtri sono ora gestiti dal componente CaviFilterableTable

  // Funzione per aprire il dialogo dei dettagli del cavo
  const handleOpenDetails = (cavo) => {
    setSelectedCavo(cavo);
    setDetailsDialogOpen(true);
  };

  // Funzione per chiudere il dialogo dei dettagli del cavo
  const handleCloseDetails = () => {
    setDetailsDialogOpen(false);
    setSelectedCavo(null);
  };

  // Funzione per chiudere la notifica
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Funzione per mostrare una notifica
  const showNotification = (message, severity = 'success') => {
    setNotification({ open: true, message, severity });
  };

  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale

  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable

  // Rimossa funzione handleViewModeChange

  // Renderizza il dialogo dei dettagli del cavo
  const renderDetailsDialog = () => {
    if (!selectedCavo) return null;

    return (
      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth>
        <DialogTitle>
          Dettagli Cavo: {selectedCavo.id_cavo}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Informazioni Generali</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>
                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}
                <Typography variant="body2"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>
                {/* sh field is now a spare field (kept in DB but hidden in UI) */}
              </Box>

              <Typography variant="subtitle1" gutterBottom>Partenza</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Arrivo</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>
              </Box>

              <Typography variant="subtitle1" gutterBottom>Installazione</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>
                <Typography variant="body2"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>
                <Typography variant="body2"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>
                <Typography variant="body2"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Chiudi</Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable

  // Renderizza il pannello delle statistiche
  const renderStatsPanel = () => {
    // Verifica che stats sia definito e abbia la struttura attesa
    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {
      return (
        <Paper sx={{ mb: 3, p: 2 }}>
          <Typography variant="h6" gutterBottom>Statistiche</Typography>
          {loadingStats ? (
            <LinearProgress />
          ) : (
            <Typography variant="body2">Nessuna statistica disponibile</Typography>
          )}
        </Paper>
      );
    }

    // Valori predefiniti per evitare errori
    const totali = stats.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 };
    const metrature = stats.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 };
    const stati = stats.stati || [];

    return (
      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Statistiche</Typography>
          {stats.revisione_utilizzata && (
            <Chip
              label={`Rev: ${stats.revisione_utilizzata}`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>
        {loadingStats ? (
          <LinearProgress />
        ) : (
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Typography variant="subtitle1" gutterBottom>Totali</Typography>
              <Typography variant="body2">Cavi Attivi: {totali.cavi_attivi || 0}</Typography>
              <Typography variant="body2">Cavi Spare: {totali.cavi_spare || 0}</Typography>
              <Typography variant="body2">Totale Cavi: {totali.cavi_totali || 0}</Typography>
              {/* Rimossa visualizzazione della revisione da qui, spostata nel titolo delle statistiche della tabella */}
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="subtitle1" gutterBottom>Metrature</Typography>
              <Typography variant="body2">Metri Teorici: {(metrature.metri_teorici_totali || 0).toFixed(2)}</Typography>
              <Typography variant="body2">Metri Posati: {(metrature.metri_reali_totali || 0).toFixed(2)}</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Box sx={{ width: '100%', mr: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={metrature.percentuale_completamento || 0}
                    sx={{ height: 10, borderRadius: 5 }}
                  />
                </Box>
                <Box sx={{ minWidth: 35 }}>
                  <Typography variant="body2" color="text.secondary">{`${(metrature.percentuale_completamento || 0).toFixed(1)}%`}</Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="subtitle1" gutterBottom>Stati</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {stati.length > 0 ? stati.map((stato, index) => (
                  <Chip
                    key={index}
                    label={`${stato.stato || 'N/A'}: ${stato.count || 0}`}
                    size="small"
                    onClick={() => {
                      setFilters(prev => ({
                        ...prev,
                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato
                      }));
                    }}
                  />
                )) : (
                  <Typography variant="body2">Nessuno stato disponibile</Typography>
                )}
              </Box>
            </Grid>
          </Grid>
        )}
      </Paper>
    );
  };

  return (
    <Box className="cavi-page">
      {loading ? (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>
          <CircularProgress size={40} />
          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            Ricarica la pagina
          </Button>
        </Box>
      ) : error ? (
        <Box>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
            {error.includes('Network Error') && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.
                <br />
                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.
              </Typography>
            )}
          </Alert>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              className="primary-button"
              onClick={() => window.location.reload()}
            >
              Ricarica la pagina
            </Button>
          </Box>
        </Box>
      ) : (
        <Box>
          {/* Selettore Revisione */}
          {revisioniDisponibili.length > 0 && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h6">Visualizzazione:</Typography>
                <FormControl size="small" sx={{ minWidth: 250 }}>
                  <InputLabel>Revisione da Visualizzare</InputLabel>
                  <Select
                    value={revisioneSelezionata || 'corrente'}
                    onChange={handleRevisioneChange}
                    label="Revisione da Visualizzare"
                  >
                    <MenuItem value="corrente">
                      📋 Revisione Corrente {revisioneCorrente && `(${revisioneCorrente})`}
                    </MenuItem>
                    {revisioniDisponibili.map((rev) => (
                      <MenuItem key={rev.revisione} value={rev.revisione}>
                        📚 {rev.revisione} ({rev.cavi_count} cavi)
                        {rev.revisione === revisioneCorrente && ' - Attuale'}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Chip
                  label={
                    revisioneSelezionata
                      ? `Storico: ${revisioneSelezionata}`
                      : `Corrente: ${stats.revisione_utilizzata || revisioneCorrente || 'N/A'}`
                  }
                  color={revisioneSelezionata ? "secondary" : "primary"}
                  variant="outlined"
                />
              </Box>
            </Paper>
          )}

          {/* Pannello Statistiche */}
          {renderStatsPanel()}

          {/* Sezione Cavi Attivi */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5">
                Cavi Attivi {caviAttivi.length > 0 ? `(${caviAttivi.length})` : ''}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => window.location.reload()}
                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
                disabled={loading}
              >
                Aggiorna
              </Button>
            </Box>
          </Box>

          {caviAttivi.length > 0 ? (
            <Box sx={{ mb: 2 }}>
              {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}
              {process.env.NODE_ENV === 'development' && (
                <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>
                  {Object.keys(caviAttivi[0]).map(key => (
                    <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>
                  ))}
                </Box>
              )}
              <CaviFilterableTable
                cavi={caviAttivi}
                loading={loading}
                onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}
                revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}
              />
            </Box>
          ) : (
            <Alert severity="info" sx={{ mb: 2 }}>
              Nessun cavo attivo trovato. I cavi attivi appariranno qui.
            </Alert>
          )}

          {/* Sezione Cavi Spare */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5">
                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => window.location.reload()}
                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
                disabled={loading}
              >
                Aggiorna
              </Button>
            </Box>
            {caviSpare.length > 0 ? (
              <CaviFilterableTable
                cavi={caviSpare}
                loading={loading}
                onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}
              />
            ) : (
              <Alert severity="info" sx={{ mb: 2 }}>
                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.
              </Alert>
            )}
          </Box>

          {/* Rimossa sezione Debug */}

          {/* Dialogo dei dettagli del cavo */}
          {renderDetailsDialog()}

          {/* Dialogo per l'eliminazione dei cavi */}
          <Dialog
            open={openEliminaCavoDialog}
            onClose={() => setOpenEliminaCavoDialog(false)}
            fullWidth
            maxWidth="md"
          >
            <PosaCaviCollegamenti
              cantiereId={cantiereId}
              onSuccess={(message) => {
                // Chiudi il dialogo
                setOpenEliminaCavoDialog(false);

                // Se c'è un messaggio, è un'operazione completata con successo
                if (message) {
                  // Mostra un messaggio di successo
                  console.log('Operazione completata:', message);
                  // Mostra un messaggio di successo con Snackbar
                  showNotification(message, 'success');
                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi
                  setTimeout(() => {
                    console.log('Ricaricamento dati dopo operazione...');
                    try {
                      // Ricarica i dati invece di ricaricare la pagina
                      fetchCavi(true);
                    } catch (error) {
                      console.error('Errore durante il ricaricamento dei dati:', error);
                      // Se fallisce, prova a ricaricare la pagina
                      window.location.reload();
                    }
                  }, 1000);
                } else {
                  // È un'operazione annullata, non mostrare messaggi
                  console.log('Operazione annullata dall\'utente');
                }
              }}
              onError={(message) => {
                // Mostra un messaggio di errore
                console.error('Errore durante l\'eliminazione del cavo:', message);
                // Mostra un alert all'utente
                alert(`Errore: ${message}`);
                // Chiudi il dialogo
                setOpenEliminaCavoDialog(false);
                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata
                fetchCavi();
              }}
              initialOption="eliminaCavo"
            />
          </Dialog>

          {/* Dialogo per la modifica dei cavi */}
          {/* Log del cantiereId prima di aprire il dialog */}
          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}

          <Dialog
            open={openModificaCavoDialog}
            onClose={() => setOpenModificaCavoDialog(false)}
            fullWidth
            maxWidth="sm"
          >
            <PosaCaviCollegamenti
              cantiereId={cantiereId}
              onSuccess={(message) => {
                // Chiudi il dialogo
                setOpenModificaCavoDialog(false);

                // Se c'è un messaggio, è un'operazione completata con successo
                if (message) {
                  // Mostra un messaggio di successo
                  console.log('Operazione completata:', message);
                  // Mostra un messaggio di successo con Snackbar
                  showNotification(message, 'success');
                  // Ricarica i dati immediatamente
                  console.log('Ricaricamento dati dopo operazione...');
                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi
                  setTimeout(() => {
                    try {
                      fetchCavi(true);
                    } catch (error) {
                      console.error('Errore durante il ricaricamento dei dati:', error);
                      // Se fallisce, prova a ricaricare la pagina
                      window.location.reload();
                    }
                  }, 1000);
                } else {
                  // È un'operazione annullata, non mostrare messaggi
                  console.log('Operazione annullata dall\'utente');
                }
              }}
              onError={(message) => {
                // Mostra un messaggio di errore
                console.error('Errore durante la modifica del cavo:', message);
                // Mostra un alert all'utente
                alert(`Errore: ${message}`);
                // Chiudi il dialogo
                setOpenModificaCavoDialog(false);
                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata
                console.log('Ricaricamento dati dopo errore...');
                fetchCavi(true);
              }}
              initialOption="modificaCavo"
            />
          </Dialog>

          {/* Dialogo per l'aggiunta di un nuovo cavo */}
          <Dialog open={openAggiungiCavoDialog} onClose={() => setOpenAggiungiCavoDialog(false)} maxWidth="sm" fullWidth>
            <PosaCaviCollegamenti
              cantiereId={cantiereId}
              onSuccess={(message) => {
                // Chiudi il dialogo
                setOpenAggiungiCavoDialog(false);

                // Se c'è un messaggio, è un'operazione completata con successo
                if (message) {
                  // Mostra un messaggio di successo
                  console.log('Operazione completata:', message);
                  // Mostra un messaggio di successo con Snackbar
                  showNotification(message, 'success');
                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi
                  setTimeout(() => {
                    console.log('Ricaricamento dati dopo operazione...');
                    try {
                      // Ricarica i dati in modalità silenziosa per evitare il "blink" della pagina
                      fetchCavi(true);
                    } catch (error) {
                      console.error('Errore durante il ricaricamento dei dati:', error);
                      // Se fallisce, prova a ricaricare la pagina immediatamente
                      console.log('Tentativo di ricaricamento della pagina...');
                      window.location.reload();
                    }
                  }, 1000);
                } else {
                  // È un'operazione annullata, non mostrare messaggi
                  console.log('Operazione annullata dall\'utente');
                }
              }}
              onError={(message) => {
                // Mostra un messaggio di errore
                console.error('Errore durante l\'aggiunta del cavo:', message);
                // Mostra un messaggio di errore con Snackbar
                showNotification(`Errore: ${message}`, 'error');
                // Chiudi il dialogo
                setOpenAggiungiCavoDialog(false);
                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata
                fetchCavi(true);
              }}
              initialOption="aggiungiCavo"
            />
          </Dialog>

          {/* Snackbar per le notifiche */}
          <Snackbar
            open={notification.open}
            autoHideDuration={4000}
            onClose={handleCloseNotification}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
              {notification.message}
            </Alert>
          </Snackbar>
        </Box>
      )}
    </Box>
  );
};

export default VisualizzaCaviPage;
