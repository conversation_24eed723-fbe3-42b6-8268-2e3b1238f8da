import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  CircularProgress,
  Alert,
  IconButton,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  FileUpload as FileUploadIcon
} from '@mui/icons-material';
import excelService from '../../services/excelService';

const ExcelPopup = ({ open, onClose, operationType, cantiereId, onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [fileInput, setFileInput] = useState(null);
  const [revisione, setRevisione] = useState('');
  const [filePath, setFilePath] = useState('');
  const [downloadUrl, setDownloadUrl] = useState('');

  // Reset state when dialog opens or operation changes
  React.useEffect(() => {
    if (open) {
      setFileInput(null);
      setFilePath('');
      setDownloadUrl('');
      setRevisione('');
      setLoading(false);
    }
  }, [open, operationType]);

  // Gestisce la chiusura del dialog
  const handleClose = () => {
    setLoading(false);
    setFileInput(null);
    setFilePath('');
    setDownloadUrl('');
    onClose();
  };

  // Gestisce il cambio del file selezionato
  const handleFileChange = (e) => {
    setFileInput(e.target.files[0]);
  };

  // Gestisce l'importazione dei cavi da Excel
  const handleImportaCavi = async () => {
    try {
      if (!fileInput) {
        onError('Seleziona un file Excel da importare');
        return;
      }

      if (!revisione.trim()) {
        onError('Inserisci il codice identificativo della revisione');
        return;
      }

      setLoading(true);
      const formData = new FormData();
      formData.append('file', fileInput);
      formData.append('revisione', revisione.trim());

      await excelService.importCavi(cantiereId, formData);
      onSuccess('Cavi importati con successo');
      handleClose();

      // Aggiorna la pagina dopo l'importazione
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      onError('Errore nell\'importazione dei cavi: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'importazione dei cavi:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'importazione del parco bobine da Excel
  const handleImportaParcoBobine = async () => {
    try {
      if (!fileInput) {
        onError('Seleziona un file Excel da importare');
        return;
      }

      setLoading(true);
      const formData = new FormData();
      formData.append('file', fileInput);

      await excelService.importParcoBobine(cantiereId, formData);
      onSuccess('Parco bobine importato con successo');
      handleClose();
    } catch (error) {
      onError('Errore nell\'importazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'importazione del parco bobine:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la creazione del template Excel per i cavi
  const handleCreaTemplateCavi = async () => {
    try {
      setLoading(true);
      const response = await excelService.createCaviTemplate();

      if (response && response.file_url) {
        setDownloadUrl(response.file_url);
        onSuccess('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.');
      } else {
        onError('Errore nella creazione del template Excel per cavi');
      }
    } catch (error) {
      onError('Errore nella creazione del template Excel per cavi: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella creazione del template Excel per cavi:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la creazione del template Excel per il parco bobine
  const handleCreaTemplateParcoBobine = async () => {
    try {
      setLoading(true);
      const response = await excelService.createParcoBobineTemplate();

      if (response && response.file_url) {
        setDownloadUrl(response.file_url);
        onSuccess('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.');
      } else {
        onError('Errore nella creazione del template Excel per parco bobine');
      }
    } catch (error) {
      onError('Errore nella creazione del template Excel per parco bobine: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella creazione del template Excel per parco bobine:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'esportazione dei cavi in Excel
  const handleEsportaCavi = async () => {
    try {
      setLoading(true);
      const response = await excelService.exportCavi(cantiereId);

      if (response && response.file_url) {
        setDownloadUrl(response.file_url);
        onSuccess('Cavi esportati con successo e download avviato! Controlla la cartella Download del tuo browser.');
      } else {
        onError('Errore nell\'esportazione dei cavi');
      }
    } catch (error) {
      onError('Errore nell\'esportazione dei cavi: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'esportazione dei cavi:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'esportazione del parco bobine in Excel
  const handleEsportaParcoBobine = async () => {
    try {
      setLoading(true);
      const response = await excelService.exportParcoBobine(cantiereId);

      if (response && response.file_url) {
        setDownloadUrl(response.file_url);
        onSuccess('Parco bobine esportato con successo e download avviato! Controlla la cartella Download del tuo browser.');
      } else {
        onError('Errore nell\'esportazione del parco bobine');
      }
    } catch (error) {
      onError('Errore nell\'esportazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'esportazione del parco bobine:', error);
    } finally {
      setLoading(false);
    }
  };

  // Esegue l'operazione selezionata
  const executeOperation = () => {
    switch (operationType) {
      case 'importaCavi':
        handleImportaCavi();
        break;
      case 'importaParcoBobine':
        handleImportaParcoBobine();
        break;
      case 'creaTemplateCavi':
        handleCreaTemplateCavi();
        break;
      case 'creaTemplateParcoBobine':
        handleCreaTemplateParcoBobine();
        break;
      case 'esportaCavi':
        handleEsportaCavi();
        break;
      case 'esportaParcoBobine':
        handleEsportaParcoBobine();
        break;
      default:
        break;
    }
  };

  // Determina il titolo del dialog in base all'operazione
  const getDialogTitle = () => {
    switch (operationType) {
      case 'importaCavi':
        return 'Importa Cavi da Excel';
      case 'importaParcoBobine':
        return 'Importa Parco Bobine da Excel';
      case 'creaTemplateCavi':
        return 'Crea Template Excel per Cavi';
      case 'creaTemplateParcoBobine':
        return 'Crea Template Excel per Parco Bobine';
      case 'esportaCavi':
        return 'Esporta Cavi in Excel';
      case 'esportaParcoBobine':
        return 'Esporta Parco Bobine in Excel';
      default:
        return 'Gestione Excel';
    }
  };

  // Determina se mostrare il selettore di file
  const showFileInput = ['importaCavi', 'importaParcoBobine'].includes(operationType);

  // Determina se mostrare il link di download
  const showDownloadLink = downloadUrl && ['creaTemplateCavi', 'creaTemplateParcoBobine', 'esportaCavi', 'esportaParcoBobine'].includes(operationType);

  // Determina il testo del pulsante di azione
  const getActionButtonText = () => {
    if (showDownloadLink) return 'Chiudi';

    switch (operationType) {
      case 'importaCavi':
      case 'importaParcoBobine':
        return 'Importa';
      case 'creaTemplateCavi':
      case 'creaTemplateParcoBobine':
        return 'Crea Template';
      case 'esportaCavi':
      case 'esportaParcoBobine':
        return 'Esporta';
      default:
        return 'Conferma';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '8px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #e0e0e0',
        pb: 1
      }}>
        <Typography variant="h6">{getDialogTitle()}</Typography>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3, pb: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>
            <CircularProgress size={40} />
            <Typography variant="body1" sx={{ mt: 2 }}>
              Elaborazione in corso...
            </Typography>
          </Box>
        ) : (
          <Box>
            {showFileInput && (
              <>
                <Typography variant="body2" paragraph>
                  Seleziona un file Excel da importare:
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<UploadIcon />}
                    sx={{ mb: 1 }}
                  >
                    Seleziona File
                    <input
                      type="file"
                      accept=".xlsx,.xls"
                      hidden
                      onChange={handleFileChange}
                    />
                  </Button>
                  {fileInput && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      File selezionato: {fileInput.name}
                    </Typography>
                  )}
                </Box>

                {operationType === 'importaCavi' && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Codice identificativo della revisione:
                    </Typography>
                    <TextField
                      fullWidth
                      size="small"
                      value={revisione}
                      onChange={(e) => setRevisione(e.target.value)}
                      placeholder="Inserisci il codice revisione (es. REV_001, V1.0, etc.)"
                      variant="outlined"
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      Questo codice identificherà univocamente questa importazione
                    </Typography>
                  </Box>
                )}

                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Assicurati che il file Excel sia nel formato corretto.
                    {operationType === 'importaCavi' && ' Puoi scaricare un template vuoto utilizzando l\'opzione "Template Cavi".'}
                    {operationType === 'importaParcoBobine' && ' Puoi scaricare un template vuoto utilizzando l\'opzione "Template Parco Bobine".'}
                    <br />
                    <strong>Nota:</strong> Il sistema riconosce automaticamente se la prima riga contiene un titolo. In tal caso, le intestazioni delle colonne sono attese nella seconda riga.
                  </Typography>
                </Alert>
              </>
            )}

            {['creaTemplateCavi', 'creaTemplateParcoBobine'].includes(operationType) && !showDownloadLink && (
              <Typography variant="body2" paragraph>
                Clicca su "{getActionButtonText()}" per generare un template Excel vuoto.
              </Typography>
            )}

            {['esportaCavi', 'esportaParcoBobine'].includes(operationType) && !showDownloadLink && (
              <Typography variant="body2" paragraph>
                Clicca su "{getActionButtonText()}" per esportare i dati in un file Excel.
              </Typography>
            )}

            {showDownloadLink && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    File generato con successo!
                  </Typography>
                </Alert>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<DownloadIcon />}
                  href={downloadUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{ mt: 1 }}
                >
                  Scarica File
                </Button>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #e0e0e0' }}>
        <Button
          onClick={handleClose}
          color="inherit"
          variant="outlined"
          sx={{ mr: 1 }}
        >
          Annulla
        </Button>
        {!showDownloadLink && (
          <Button
            onClick={executeOperation}
            color="primary"
            variant="contained"
            startIcon={showFileInput ? <FileUploadIcon /> : <DownloadIcon />}
            disabled={loading || (showFileInput && !fileInput) || (operationType === 'importaCavi' && !revisione.trim())}
          >
            {getActionButtonText()}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ExcelPopup;
