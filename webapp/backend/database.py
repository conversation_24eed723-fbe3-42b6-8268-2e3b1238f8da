from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import logging

from config import settings

# Configura il logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Crea l'engine SQLAlchemy
try:
    logger.info(f"Tentativo di connessione al database: {settings.DATABASE_URL}")
    engine = create_engine(settings.DATABASE_URL, echo=True)
    logger.info("Connessione al database riuscita")

    # Crea una sessione locale
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Base per i modelli
    Base = declarative_base()

    # Funzione per ottenere una sessione del database
    def get_db():
        """
        Funzione di utilità per ottenere una sessione del database.
        Viene utilizzata come dipendenza nelle route FastAPI.
        """
        db = SessionLocal()
        try:
            logger.info("Nuova sessione del database creata")
            yield db
        except Exception as e:
            logger.error(f"Errore durante l'utilizzo della sessione del database: {e}")
            raise
        finally:
            logger.info("Sessione del database chiusa")
            db.close()

except Exception as e:
    logger.error(f"Errore durante la connessione al database: {e}")
    raise
